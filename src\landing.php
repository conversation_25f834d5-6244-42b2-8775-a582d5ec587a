<?php session_start();

/** @var PDO $conexion */
global $conexion;

use App\classes\Configuracion;

require_once dirname(__FILE__, 2) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/general/preparar_web.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en form_aliados_comerciales.php (POST).");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	echo json_encode($response);
	exit;
}

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
	try {
		if (isset($_GET['d'])) {
			// Get the PDF filename from configuration
			try {
				$pdf_config = Configuracion::get_by_descripcion('PDF Plan de negocios', $conexion);

				if ($pdf_config && $pdf_config->getValor()) {
					$filename = $pdf_config->getValor();
					$filePath = Configuracion::URL_CARPETA_PDF_NEGOCIOS . $filename;

					// Check if the file exists
					if (file_exists($filePath)) {
						// Set headers to force download
						header('Content-Type: application/pdf');
						header('Content-Disposition: attachment; filename="' . basename($filename) . '"');
						header('Content-Length: ' . filesize($filePath));

						// Read and output the file
						readfile($filePath);
						exit;
					} else {
						echo "Error: El archivo PDF del plan de negocios no se encuentra en el servidor.";
					}
				} else {
					// Fallback to the old file if configuration doesn't exist
					$file = 'plan_negocios.pdf';
					$filePath = __ROOT__ . '/resources/docs/' . $file;

					if (file_exists($filePath)) {
						// Set headers to force download
						header('Content-Type: application/pdf');
						header('Content-Disposition: attachment; filename="' . basename($file) . '"');
						header('Content-Length: ' . filesize($filePath));

						// Read and output the file
						readfile($filePath);
						exit;
					} else {
						echo "Error: No se ha configurado un archivo PDF del plan de negocios.";
					}
				}
			} catch (Exception $e) {
				echo "Error: " . $e->getMessage();
			}
		}

	} catch (Exception $e) {
		$error_display = 'show';
		$error_text    = $e->getMessage();
	}
}
#endregion get
#region try
try {


} catch (Exception $e) {
	$error_display = 'show';
	$error_text    = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/landing/landing.view.php';

?>