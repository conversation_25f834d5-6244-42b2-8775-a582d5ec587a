<?php

declare(strict_types=1);

namespace App\classes;

use InvalidArgumentException;
use Exception;
use PDO;
use PDOException;

class Configuracion
{
	private ?int    $id          = null;
	private ?string $descripcion = null;
	private ?string $valor       = null;
	private ?int    $es_editable = null;
	const URL_CARPETA_PDF_NEGOCIOS     = __ROOT__ . '/resources/uploads/documentos/';
	const URL_WEB_CARPETA_PDF_NEGOCIOS = '../resources/uploads/documentos/';

	public function __construct()
	{
		$this->id          = 0;
		$this->es_editable = 1; // Default to 1 as per SQL
		$this->descripcion = null;
		$this->valor       = null;
	}

	/**
	 * @throws Exception
	 */
	public static function construct(array $resultado = []): self
	{
		try {
			$objeto = new self();
			$objeto->id = $resultado['id'] ?? 0;
			$objeto->descripcion = $resultado['descripcion'] ?? null;
			$objeto->valor = $resultado['valor'] ?? null;
			$objeto->es_editable = isset($resultado['es_editable']) ? (int)$resultado['es_editable'] : 1;

			return $objeto;

		} catch (Exception $e) {
			throw new Exception("Error al construir Configuracion: " . $e->getMessage());
		}
	}

	/**
	 * @throws Exception
	 */
	public static function get(int $id, PDO $conexion): ?self
	{
		try {
			$query = <<<SQL
            SELECT
            	*
            FROM configuraciones
            WHERE
            	id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id", $id, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			if ($resultado) {
				return self::construct($resultado);
			} else {
				return null; // Return null for not found
			}
		} catch (Exception $e) {
			throw new Exception("Error al obtener Configuracion: " . $e->getMessage());
		}
	}

	/**
	 * @throws Exception
	 */
	public static function get_by_descripcion(string $descripcion, PDO $conexion): ?self
	{
		try {
			$query = <<<SQL
            SELECT
            	*
            FROM configuraciones
            WHERE
            	descripcion = :descripcion
            LIMIT 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':descripcion', $descripcion, PDO::PARAM_STR);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			if ($resultado) {
				return self::construct($resultado);
			} else {
				return null; // Return null for not found
			}
		} catch (Exception $e) {
			throw new Exception("Error al obtener Configuracion: " . $e->getMessage());
		}
	}

	/**
	 * @throws Exception
	 */
	public static function get_list(array $parametros, PDO $conexion): array
	{
		try {
			$descripcion = $parametros['descripcion'] ?? '';

			$query = <<<SQL
            SELECT
            	c.*
            FROM configuraciones c
            SQL;

			// Add WHERE clause if description filter is provided
			if (!empty($descripcion)) {
				$query .= " WHERE c.descripcion = :descripcion ";
			}

			// Add ORDER BY clause
			$query .= " ORDER BY c.id ASC";

			$statement = $conexion->prepare($query);

			// Bind description parameter if filter is provided
			if (!empty($descripcion)) {
				$statement->bindValue(':descripcion', $descripcion, PDO::PARAM_STR);
			}

			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			if (!$resultados) {
				return [];
			} else {
				$listado = [];

				foreach ($resultados as $resultado) {
					$configuracion = self::construct($resultado);
					$listado[]     = $configuracion;
				}

				return $listado;
			}
		} catch (Exception $e) {
			throw new Exception("Error al obtener lista de Configuraciones: " . $e->getMessage());
		}
	}

	/**
	 * Guarda esta configuración (instancia actual) en la base de datos.
	 * Si el ID es 0 o null, inserta un nuevo registro. Si el ID existe, actualiza el registro.
	 * Actualiza el ID del objeto tras la inserción si es un nuevo registro.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True en éxito, false en fallo.
	 * @throws Exception Si los datos requeridos están vacíos o hay error en DB.
	 */
	public function guardar(PDO $conexion): bool
	{
		// Validaciones básicas
		if (empty($this->getDescripcion())) {
			throw new Exception("La descripción es requerida para guardar una configuración.");
		}

		try {
			if ($this->getId() === null || $this->getId() === 0) {
				// --- INSERT ---
				$query = <<<SQL
                 INSERT INTO configuraciones ( 
                      descripcion,
                      valor,
                      es_editable
                 ) VALUES ( 
                      :descripcion,
                      :valor,
                      :es_editable
                 )
                 SQL;

				$statement = $conexion->prepare($query);
				$statement->bindValue(':descripcion', $this->getDescripcion(), PDO::PARAM_STR);
				$statement->bindValue(':valor', $this->getValor(), PDO::PARAM_STR);
				$statement->bindValue(':es_editable', $this->getEsEditable(), PDO::PARAM_INT);

				$success = $statement->execute();

				if ($success) {
					$this->setId((int)$conexion->lastInsertId());
					return true;
				} else {
					error_log("Error al ejecutar INSERT para configuracion: " . implode(" | ", $statement->errorInfo()));
					return false;
				}

			} else {
				// --- UPDATE ---
				// Only update the valor field, not the descripcion
				// If es_editable is to be updated, it should be included here.
				// Assuming es_editable can also be changed via the UI, we'll add it to the update.
				$query = <<<SQL
                 UPDATE configuraciones SET
                     valor = :valor, es_editable = :es_editable
                 WHERE
                      id = :id
                 SQL;

				$statement = $conexion->prepare($query);
				$statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);
				$statement->bindValue(':valor', $this->getValor(), PDO::PARAM_STR);
				$statement->bindValue(':es_editable', $this->getEsEditable(), PDO::PARAM_INT);

				return $statement->execute();
			}

		} catch (PDOException $e) {
			$infoParaError = $this->getDescripcion() ?? '[desconocido]';
			throw new Exception("Error de base de datos al guardar configuración '$infoParaError': " . $e->getMessage());
		} catch (Exception $e) {
			throw new Exception("Error general al guardar configuración: " . $e->getMessage());
		}
	}

	/**
	 * Deletes a configuration by its ID.
	 *
	 * @param int $id       The ID of the configuration to delete.
	 * @param PDO $conexion The PDO database connection object.
	 *
	 * @return bool True on success, false on failure.
	 * @throws Exception If the ID is invalid or a database error occurs.
	 */
	public static function eliminar(int $id, PDO $conexion): bool
	{
		if ($id <= 0) {
			throw new InvalidArgumentException("ID Configuracion invalido.");
		}

		try {
			$query = <<<SQL
            DELETE FROM configuraciones
            WHERE
            	id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			return $statement->execute();

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al eliminar la configuración: " . $e->getMessage(), 0, $e);
		} catch (Exception $e) {
			throw new Exception("Error inesperado al eliminar la configuración: " . $e->getMessage(), 0, $e);
		}
	}

	// --- GETTERS AND SETTERS ---

	public function getId(): ?int
	{
		return $this->id;
	}

	public function setId(?int $id): self
	{
		$this->id = $id;
		return $this;
	}

	public function getDescripcion(): ?string
	{
		return $this->descripcion;
	}

	public function setDescripcion(?string $descripcion): self
	{
		$this->descripcion = $descripcion;
		return $this;
	}

	public function getValor(): ?string
	{
		return $this->valor;
	}

	public function setValor(?string $valor): self
	{
		$this->valor = $valor;
		return $this;
	}

	public function getEsEditable(): ?int
	{
		return $this->es_editable;
	}

	public function setEsEditable(?int $es_editable): self
	{
		$this->es_editable = $es_editable;
		return $this;
	}
}
