<?php

declare(strict_types=1);

use App\classes\Tarea;
use App\classes\Proyecto;
use App\classes\Sprint;
use App\classes\TareaAgente;

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 3) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en ltareas.php.");
	die('Error crítico: No se pudo conectar a la base de datos.');
}

#region region init variables
$tareas_historicas      = [];
$filtro_proyecto_id     = null;
$filtro_sprint_id       = filter_input(INPUT_POST, 'sprint', FILTER_VALIDATE_INT);
$filtro_fecha_inicio    = filter_input(INPUT_POST, 'fecha_inicio', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
$filtro_fecha_fin       = filter_input(INPUT_POST, 'fecha_fin', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
$filtro_busqueda        = filter_input(INPUT_POST, 'busqueda', FILTER_SANITIZE_FULL_SPECIAL_CHARS);
$nombre_proyecto_filtro = '';
$sprints_terminados     = [];
$success_text           = '';
$success_display        = 'none';
$error_text             = '';
$error_display          = 'none';
#endregion init variables

#region region Handle Flash Message Success
if (!empty($_SESSION['flash_message_success'])) {
	$success_text = $_SESSION['flash_message_success'];
	$success_display = 'show';
	unset($_SESSION['flash_message_success']);
}
#endregion Handle Flash Message Success

#region region Handle Flash Message Error
if (!empty($_SESSION['flash_message_error'])) {
	$error_text = $_SESSION['flash_message_error'];
	$error_display = 'show';
	unset($_SESSION['flash_message_error']);
}
#endregion Handle Flash Message Error

#region region Handle AJAX Requests for TareaAgente
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action'])) {
	$action = $_POST['action'];
	$isAjax = isset($_POST['is_ajax']) && $_POST['is_ajax'] == '1';

	if ($isAjax && $action === 'get_tarea_agentes') {
		header('Content-Type: application/json');

		$tareaId = filter_input(INPUT_POST, 'tarea_id', FILTER_VALIDATE_INT);

		if (!$tareaId) {
			echo json_encode(['success' => false, 'message' => 'ID de tarea inválido']);
			exit;
		}

		try {
			$tareaAgentes = TareaAgente::getByTareaIdWithAgentDescriptions($tareaId, $conexion);

			// Get task information to include module data
			$tarea = Tarea::get($tareaId, $conexion);
			$modulo_nombre = $tarea ? $tarea->getNombreProyectoModulo() : 'N/A';

			echo json_encode([
				'success' => true,
				'tarea_agentes' => array_map(function($ta) use ($modulo_nombre) {
					return [
						'id' => $ta->getId(),
						'id_agente' => $ta->getIdAgente(),
						'agente_descripcion' => $ta->getAgenteDescripcion(),
						'modulo_nombre' => $modulo_nombre,
						'costo_usd' => $ta->getCostoUsd(),
						'n_mensajes' => $ta->getNMensajes()
					];
				}, $tareaAgentes)
			]);
		} catch (Exception $e) {
			echo json_encode(['success' => false, 'message' => 'Error al obtener agentes: ' . $e->getMessage()]);
		}
		exit;
	}
}
#endregion Handle AJAX Requests

#region region Get Project Filter from Session
if (isset($_SESSION['filtro_proyecto_historico_id'])) {
	$filtro_proyecto_id = $_SESSION['filtro_proyecto_historico_id'];

	try {
		$proyecto = Proyecto::get($filtro_proyecto_id, $conexion);
		if ($proyecto) {
			$nombre_proyecto_filtro = $proyecto->getDescripcion();
		}
	} catch (Exception $e) {
		$error_text = "Error al obtener información del proyecto: " . $e->getMessage();
		$error_display = 'show';
	}
}
#endregion Get Project Filter

#region region Fetch Historical Tasks
try {
	if ($filtro_proyecto_id && ($filtro_sprint_id || $filtro_fecha_inicio || $filtro_fecha_fin || $filtro_busqueda)) {
		$parametros = [
			'id_proyecto'  => $filtro_proyecto_id,
			'id_sprint'    => $filtro_sprint_id,
			'fecha_inicio' => $filtro_fecha_inicio,
			'fecha_fin'    => $filtro_fecha_fin,
			'busqueda'     => $filtro_busqueda
		];

		$tareas_historicas = Tarea::getHistoricalTasks($parametros, $conexion);
	}

	if ($filtro_proyecto_id) {
		$sprints_terminados = Sprint::getFinishedSprintsByProject($filtro_proyecto_id, $conexion);
	}

} catch (Exception $e) {
	$error_display = 'show';
	$error_text = "Error al obtener las tareas históricas: " . $e->getMessage();
}
#endregion Fetch Historical Tasks

function getTareaAgentesCount($tareaId, $conexion) {
	try {
		return TareaAgente::countByTareaId($tareaId, $conexion);
	} catch (Exception) {
		return 0;
	}
}

require_once __ROOT__ . '/views/admin/ltareas_historico.view.php';

?>
