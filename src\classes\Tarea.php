<?php

declare(strict_types=1);

namespace App\classes;

use Exception;
use PDO;
use PDOException;

class Tarea
{
	// --- Atributos ---
	private ?int    $id                    = null;
	private ?string $descripcion           = null;
	private ?int    $id_tarea_estado       = null;
	private ?string $nombre_tarea_estado   = null;
	private ?string $bg_color              = null;
	private ?int    $id_proyecto           = null;
	private ?string $nombre_proyecto       = null;
	private ?int    $id_proyecto_modulo    = null;
	private ?string $nombre_proyecto_modulo = null;
	private ?int    $id_tarea_padre        = null;
	private ?string $nombre_tarea_padre    = null;
	private ?int    $id_sprint             = null;
	private ?string $nombre_sprint         = null;
	private ?string $fecha_creacion        = null;
	private ?string $fecha_terminacion     = null;
	const ESTADO_PENDIENTE   = 1;
	const ESTADO_EN_PROGRESO = 2;
	const ESTADO_TERMINADO   = 3;
	const ESTADO_ELIMINADO   = 4;

	/**
	 * Constructor: Inicializa las propiedades del objeto Tarea.
	 */
	public function __construct()
	{
		$this->id                    = null;
		$this->descripcion           = null;
		$this->id_tarea_estado       = null;
		$this->nombre_tarea_estado   = null;
		$this->bg_color              = null;
		$this->id_proyecto           = null;
		$this->nombre_proyecto       = null;
		$this->id_proyecto_modulo    = null;
		$this->nombre_proyecto_modulo = null;
		$this->id_tarea_padre        = null;
		$this->nombre_tarea_padre    = null;
		$this->id_sprint             = null;
		$this->nombre_sprint         = null;
		$this->fecha_creacion        = null; // Se establecerá automáticamente en la BD
		$this->fecha_terminacion     = null;
	}

	/**
	 * Método estático para construir un objeto Tarea desde un array (ej. fila de DB).
	 *
	 * @param array $resultado Array asociativo con los datos de la tarea.
	 *
	 * @return self Instancia de Tarea.
	 * @throws Exception Si ocurre un error durante la construcción.
	 */
	public static function construct(array $resultado = []): self
	{
		try {
			$objeto                        = new self();
			$objeto->id                    = isset($resultado['id']) ? (int)$resultado['id'] : null;
			$objeto->descripcion           = $resultado['descripcion'] ?? null;
			$objeto->id_tarea_estado       = isset($resultado['id_tarea_estado']) ? (int)$resultado['id_tarea_estado'] : null;
			$objeto->nombre_tarea_estado   = $resultado['nombre_tarea_estado'] ?? null;
			$objeto->bg_color              = $resultado['bg_color'] ?? null;
			$objeto->id_proyecto           = isset($resultado['id_proyecto']) ? (int)$resultado['id_proyecto'] : null;
			$objeto->nombre_proyecto       = $resultado['nombre_proyecto'] ?? null;
			$objeto->id_proyecto_modulo    = isset($resultado['id_proyecto_modulo']) ? (int)$resultado['id_proyecto_modulo'] : null;
			$objeto->nombre_proyecto_modulo = $resultado['nombre_proyecto_modulo'] ?? null;
			$objeto->id_tarea_padre        = isset($resultado['id_tarea_padre']) ? (int)$resultado['id_tarea_padre'] : null;
			$objeto->nombre_tarea_padre    = $resultado['nombre_tarea_padre'] ?? null;
			$objeto->id_sprint             = isset($resultado['id_sprint']) ? (int)$resultado['id_sprint'] : null;
			$objeto->nombre_sprint         = $resultado['nombre_sprint'] ?? null;
			$objeto->fecha_creacion        = $resultado['fecha_creacion'] ?? null;
			$objeto->fecha_terminacion     = $resultado['fecha_terminacion'] ?? null;

			return $objeto;
		} catch (Exception $e) {
			throw new Exception("Error al construir Tarea: " . $e->getMessage());
		}
	}

	// --- Métodos de Acceso a Datos ---

	/**
	 * Obtiene una tarea por su ID.
	 *
	 * @param int $id       ID de la tarea.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return self|null Objeto Tarea o null si no se encuentra.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get(int $id, PDO $conexion): ?self
	{
		try {
			// Consulta para obtener tarea por ID con información relacionada
			$query = <<<SQL
            SELECT
            	t.*,
            	p.descripcion AS nombre_proyecto,
            	pm.descripcion AS nombre_proyecto_modulo,
            	te.descripcion AS nombre_tarea_estado,
            	te.bg_color,
            	tp.descripcion AS nombre_tarea_padre,
            	s.descripcion AS nombre_sprint
            FROM tareas t
            LEFT JOIN proyectos p ON t.id_proyecto = p.id
            LEFT JOIN proyectos_modulos pm ON t.id_proyecto_modulo = pm.id
            LEFT JOIN tareas_estados te ON t.id_tarea_estado = te.id
            LEFT JOIN tareas tp ON t.id_tarea_padre = tp.id
            LEFT JOIN sprints s ON t.id_sprint = s.id
            WHERE
            	t.id = :id
            LIMIT 1
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(":id", $id, PDO::PARAM_INT);
			$statement->execute();
			$resultado = $statement->fetch(PDO::FETCH_ASSOC);

			return $resultado ? self::construct($resultado) : null;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener Tarea (ID: $id): " . $e->getMessage());
		}
	}

	/**
	 * Obtiene una lista de tareas.
	 *
	 * @param array $parametros
	 * @param PDO   $conexion Conexión PDO.
	 *
	 * @return array Array de objetos Tarea.
	 * @throws Exception Si hay error en DB.
	 */
	public static function get_list(array $parametros, PDO $conexion): array
	{
		try {
			$id_tarea_estado    = $parametros['id_tarea_estado'] ?? null;
			$id_proyecto        = $parametros['id_proyecto'] ?? null;
			$id_proyecto_modulo = $parametros['id_proyecto_modulo'] ?? null;
			$id_tarea_padre     = $parametros['id_tarea_padre'] ?? null;
			$id_sprint          = $parametros['id_sprint'] ?? null;

			// Base de la consulta con JOINs para obtener información relacionada
			$query  = <<<SQL
			SELECT
				t.*,
				p.descripcion AS nombre_proyecto,
				pm.descripcion AS nombre_proyecto_modulo,
				te.descripcion AS nombre_tarea_estado,
				te.bg_color,
				tp.descripcion AS nombre_tarea_padre,
				s.descripcion AS nombre_sprint
			FROM tareas t
			LEFT JOIN proyectos p ON t.id_proyecto = p.id
			LEFT JOIN proyectos_modulos pm ON t.id_proyecto_modulo = pm.id
			LEFT JOIN tareas_estados te ON t.id_tarea_estado = te.id
			LEFT JOIN tareas tp ON t.id_tarea_padre = tp.id
			LEFT JOIN sprints s ON t.id_sprint = s.id
			WHERE t.id_tarea_estado <> 4
				AND (t.id_sprint IS NULL OR s.terminado = 0 OR s.terminado IS NULL)
			SQL;

			$params = [];
			$where  = [];

			// Agregar filtros si se proporcionan
			if ($id_tarea_estado !== null) {
				$where[]                    = "t.id_tarea_estado = :id_tarea_estado";
				$params[':id_tarea_estado'] = $id_tarea_estado;
			}

			if ($id_proyecto !== null) {
				$where[]                = "t.id_proyecto = :id_proyecto";
				$params[':id_proyecto'] = $id_proyecto;
			}

			if ($id_proyecto_modulo !== null) {
				$where[]                       = "t.id_proyecto_modulo = :id_proyecto_modulo";
				$params[':id_proyecto_modulo'] = $id_proyecto_modulo;
			}

			if ($id_tarea_padre !== null) {
				$where[]                   = "t.id_tarea_padre = :id_tarea_padre";
				$params[':id_tarea_padre'] = $id_tarea_padre;
			}

			if ($id_sprint !== null) {
				$where[]              = "t.id_sprint = :id_sprint";
				$params[':id_sprint'] = $id_sprint;
			}

			// Construir la cláusula WHERE si hay filtros
			if (!empty($where)) {
				$query .= " AND " . implode(" AND ", $where);
			}

			// Ordenar por fecha de creación descendente (más recientes primero)
			$query .= " ORDER BY t.fecha_creacion DESC";

			$statement = $conexion->prepare($query);

			// Bind de parámetros si existen
			foreach ($params as $param => $value) {
				$statement->bindValue($param, $value);
			}

			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener lista de Tareas: " . $e->getMessage());
		}
	}

	/**
	 * Crea una nueva tarea en la base de datos.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return int|false El ID de la nueva tarea creada o false en caso de error.
	 * @throws Exception Si los datos requeridos están vacíos o hay error en DB.
	 */
	public function crear(PDO $conexion): int|false
	{
		// Validaciones básicas
		if (empty($this->getDescripcion()) || $this->getIdTareaEstado() === null) {
			throw new Exception("La descripción y el estado son campos requeridos para crear una tarea.");
		}

		if ($this->getIdProyecto() === null) {
			throw new Exception("El proyecto es requerido para crear una tarea.");
		}

		try {
			// Establecer la zona horaria para Colombia
			date_default_timezone_set('America/Bogota');

			// Preparar la consulta INSERT
			$query = <<<SQL
            INSERT INTO tareas (
            	 descripcion
            	,id_tarea_estado
            	,id_proyecto
            	,id_proyecto_modulo
            	,id_tarea_padre
            	,id_sprint
            ) VALUES (
            	 :descripcion
            	,:id_tarea_estado
            	,:id_proyecto
            	,:id_proyecto_modulo
            	,:id_tarea_padre
            	,:id_sprint
            )
            SQL;

			$statement = $conexion->prepare($query);

			// Bind de parámetros
			$statement->bindValue(':descripcion', $this->getDescripcion(), PDO::PARAM_STR);
			$statement->bindValue(':id_tarea_estado', $this->getIdTareaEstado(), PDO::PARAM_INT);
			$statement->bindValue(':id_proyecto', $this->getIdProyecto(), PDO::PARAM_INT);
			$statement->bindValue(':id_proyecto_modulo', $this->getIdProyectoModulo(),
				$this->getIdProyectoModulo() === null ? PDO::PARAM_NULL : PDO::PARAM_INT);
			$statement->bindValue(':id_tarea_padre', $this->getIdTareaPadre(),
				$this->getIdTareaPadre() === null ? PDO::PARAM_NULL : PDO::PARAM_INT);
			$statement->bindValue(':id_sprint', $this->getIdSprint(),
				$this->getIdSprint() === null ? PDO::PARAM_NULL : PDO::PARAM_INT);

			// Ejecutar la consulta
			$success = $statement->execute();

			if ($success) {
				// Devolver el ID de la tarea recién creada
				return (int)$conexion->lastInsertId();
			} else {
				return false; // Error en la ejecución
			}

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al crear tarea: " . $e->getMessage());
		}
	}

	/**
	 * Modifica una tarea existente.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la modificación fue exitosa, False en caso contrario.
	 * @throws Exception Si los datos requeridos están vacíos o hay error en DB.
	 */
	public function modificar(PDO $conexion): bool
	{
		// Validaciones básicas
		if ($this->getId() === null) {
			throw new Exception("El ID de la tarea es requerido para modificarla.");
		}

		if (empty($this->getDescripcion()) || $this->getIdTareaEstado() === null) {
			throw new Exception("La descripción y el estado son campos requeridos para modificar una tarea.");
		}

		if ($this->getIdProyecto() === null) {
			throw new Exception("El proyecto es requerido para modificar una tarea.");
		}

		try {
			// Establecer la zona horaria para Colombia
			date_default_timezone_set('America/Bogota');

			// Preparar la consulta UPDATE
			$query = <<<SQL
            UPDATE tareas SET
            	 descripcion = :descripcion
            	,id_tarea_estado = :id_tarea_estado
            	,id_proyecto = :id_proyecto
            	,id_proyecto_modulo = :id_proyecto_modulo
            	,id_tarea_padre = :id_tarea_padre
            	,id_sprint = :id_sprint
            WHERE
            	id = :id
            SQL;

			$statement = $conexion->prepare($query);

			// Bind de parámetros
			$statement->bindValue(':descripcion', $this->getDescripcion(), PDO::PARAM_STR);
			$statement->bindValue(':id_tarea_estado', $this->getIdTareaEstado(), PDO::PARAM_INT);
			$statement->bindValue(':id_proyecto', $this->getIdProyecto(), PDO::PARAM_INT);
			$statement->bindValue(':id_proyecto_modulo', $this->getIdProyectoModulo(),
				$this->getIdProyectoModulo() === null ? PDO::PARAM_NULL : PDO::PARAM_INT);
			$statement->bindValue(':id_tarea_padre', $this->getIdTareaPadre(),
				$this->getIdTareaPadre() === null ? PDO::PARAM_NULL : PDO::PARAM_INT);
			$statement->bindValue(':id_sprint', $this->getIdSprint(),
				$this->getIdSprint() === null ? PDO::PARAM_NULL : PDO::PARAM_INT);
			$statement->bindValue(':id', $this->getId(), PDO::PARAM_INT);

			// Ejecutar la consulta
			return $statement->execute();

		} catch (PDOException $e) {
			throw new Exception("Error de base de datos al modificar tarea (ID: {$this->getId()}): " . $e->getMessage());
		}
	}

	/**
	 * Marca una tarea como terminada, estableciendo la fecha de terminación.
	 *
	 * @param int $id       ID de la tarea a marcar como terminada.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la operación fue exitosa, False en caso contrario.
	 * @throws Exception Si ocurre un error de base de datos.
	 */
	public static function marcarTerminada(int $id, PDO $conexion): bool
	{
		try {
			// Establecer la zona horaria para Colombia
			date_default_timezone_set('America/Bogota');

			// Consulta para actualizar el estado y la fecha de terminación
			$query = <<<SQL
            UPDATE tareas SET
            	id_tarea_estado = :id_tarea_estado,
            	fecha_terminacion = NOW()
            WHERE
            	id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_tarea_estado', self::ESTADO_TERMINADO, PDO::PARAM_INT);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			return $statement->execute();

		} catch (PDOException $e) {
			throw new Exception("Error al marcar tarea como terminada (ID: $id): " . $e->getMessage());
		}
	}

	/**
	 * Marca una tarea como en progreso.
	 *
	 * @param int $id       ID de la tarea a marcar como en progreso.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la operación fue exitosa, False en caso contrario.
	 * @throws Exception Si ocurre un error de base de datos.
	 */
	public static function marcarEnProgreso(int $id, PDO $conexion): bool
	{
		try {
			// Consulta para actualizar el estado
			$query = <<<SQL
            UPDATE tareas SET
            	id_tarea_estado = :id_tarea_estado
            WHERE
            	id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_tarea_estado', self::ESTADO_EN_PROGRESO, PDO::PARAM_INT);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			return $statement->execute();

		} catch (PDOException $e) {
			throw new Exception("Error al marcar tarea como en progreso (ID: $id): " . $e->getMessage());
		}
	}

	/**
	 * Marca una tarea como eliminada.
	 *
	 * @param int $id       ID de la tarea a marcar como eliminada.
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return bool True si la operación fue exitosa, False en caso contrario.
	 * @throws Exception Si ocurre un error de base de datos.
	 */
	public static function eliminar(int $id, PDO $conexion): bool
	{
		try {
			// Consulta para actualizar el estado y la fecha de terminación
			$query = <<<SQL
            UPDATE tareas SET
            	id_tarea_estado = :id_tarea_estado
            WHERE
            	id = :id
            SQL;

			$statement = $conexion->prepare($query);
			$statement->bindValue(':id_tarea_estado', self::ESTADO_ELIMINADO, PDO::PARAM_INT);
			$statement->bindValue(':id', $id, PDO::PARAM_INT);

			return $statement->execute();

		} catch (PDOException $e) {
			throw new Exception("Error al marcar tarea como terminada (ID: $id): " . $e->getMessage());
		}
	}

	// --- Getters y Setters ---

	public function getId(): ?int
	{
		return $this->id;
	}

	public function setId(?int $id): self
	{
		$this->id = $id;
		return $this;
	}

	public function getDescripcion(): ?string
	{
		return $this->descripcion;
	}

	public function setDescripcion(?string $descripcion): self
	{
		$this->descripcion = $descripcion;
		return $this;
	}

	public function getIdTareaEstado(): ?int
	{
		return $this->id_tarea_estado;
	}

	public function setIdTareaEstado(?int $id_tarea_estado): self
	{
		$this->id_tarea_estado = $id_tarea_estado;
		return $this;
	}

	public function getNombreTareaEstado(): ?string
	{
		return $this->nombre_tarea_estado;
	}

	public function setNombreTareaEstado(?string $nombre_tarea_estado): self
	{
		$this->nombre_tarea_estado = $nombre_tarea_estado;
		return $this;
	}

	public function getBgColor(): ?string
	{
		return $this->bg_color;
	}

	public function setBgColor(?string $bg_color): self
	{
		$this->bg_color = $bg_color;
		return $this;
	}

	public function getIdProyecto(): ?int
	{
		return $this->id_proyecto;
	}

	public function setIdProyecto(?int $id_proyecto): self
	{
		$this->id_proyecto = $id_proyecto;
		return $this;
	}

	public function getNombreProyecto(): ?string
	{
		return $this->nombre_proyecto;
	}

	public function setNombreProyecto(?string $nombre_proyecto): self
	{
		$this->nombre_proyecto = $nombre_proyecto;
		return $this;
	}

	public function getIdProyectoModulo(): ?int
	{
		return $this->id_proyecto_modulo;
	}

	public function setIdProyectoModulo(?int $id_proyecto_modulo): self
	{
		$this->id_proyecto_modulo = $id_proyecto_modulo;
		return $this;
	}

	public function getNombreProyectoModulo(): ?string
	{
		return $this->nombre_proyecto_modulo;
	}

	public function setNombreProyectoModulo(?string $nombre_proyecto_modulo): self
	{
		$this->nombre_proyecto_modulo = $nombre_proyecto_modulo;
		return $this;
	}

	public function getIdTareaPadre(): ?int
	{
		return $this->id_tarea_padre;
	}

	public function setIdTareaPadre(?int $id_tarea_padre): self
	{
		$this->id_tarea_padre = $id_tarea_padre;
		return $this;
	}

	public function getNombreTareaPadre(): ?string
	{
		return $this->nombre_tarea_padre;
	}

	public function setNombreTareaPadre(?string $nombre_tarea_padre): self
	{
		$this->nombre_tarea_padre = $nombre_tarea_padre;
		return $this;
	}

	public function getFechaCreacion(): ?string
	{
		return $this->fecha_creacion;
	}

	public function setFechaCreacion(?string $fecha_creacion): self
	{
		$this->fecha_creacion = $fecha_creacion;
		return $this;
	}

	public function getFechaTerminacion(): ?string
	{
		return $this->fecha_terminacion;
	}

	public function setFechaTerminacion(?string $fecha_terminacion): self
	{
		$this->fecha_terminacion = $fecha_terminacion;
		return $this;
	}

	public function getIdSprint(): ?int
	{
		return $this->id_sprint;
	}

	public function setIdSprint(?int $id_sprint): self
	{
		$this->id_sprint = $id_sprint;
		return $this;
	}

	public function getNombreSprint(): ?string
	{
		return $this->nombre_sprint;
	}

	public function setNombreSprint(?string $nombre_sprint): self
	{
		$this->nombre_sprint = $nombre_sprint;
		return $this;
	}

	// --- Parent-Child Relationship Methods ---

	/**
	 * Obtiene las tareas hijas de una tarea padre.
	 *
	 * @param int $id_tarea_padre ID de la tarea padre.
	 * @param PDO $conexion       Conexión PDO.
	 *
	 * @return array Array de objetos Tarea que son hijas de la tarea padre.
	 * @throws Exception Si hay error en DB.
	 */
	public static function getChildTasks(int $id_tarea_padre, PDO $conexion): array
	{
		return self::get_list(['id_tarea_padre' => $id_tarea_padre], $conexion);
	}

	/**
	 * Obtiene todas las tareas que pueden ser padres (excluyendo la tarea actual y sus descendientes).
	 *
	 * @param int|null $exclude_id ID de la tarea a excluir (para evitar auto-referencia).
	 * @param PDO      $conexion   Conexión PDO.
	 *
	 * @return array Array de objetos Tarea que pueden ser padres.
	 * @throws Exception Si hay error en DB.
	 */
	public static function getPotentialParentTasks(?int $exclude_id, PDO $conexion): array
	{
		try {
			$query = <<<SQL
			SELECT
				t.*,
				p.descripcion AS nombre_proyecto,
				pm.descripcion AS nombre_proyecto_modulo,
				te.descripcion AS nombre_tarea_estado,
				te.bg_color,
				tp.descripcion AS nombre_tarea_padre,
				s.descripcion AS nombre_sprint
			FROM tareas t
			LEFT JOIN proyectos p ON t.id_proyecto = p.id
			LEFT JOIN proyectos_modulos pm ON t.id_proyecto_modulo = pm.id
			LEFT JOIN tareas_estados te ON t.id_tarea_estado = te.id
			LEFT JOIN tareas tp ON t.id_tarea_padre = tp.id
			LEFT JOIN sprints s ON t.id_sprint = s.id
			WHERE t.id_tarea_estado NOT IN (:estado_eliminado)
				AND (t.id_sprint IS NULL OR s.terminado = 0 OR s.terminado IS NULL)
			SQL;

			$params = [':estado_eliminado' => self::ESTADO_ELIMINADO];

			if ($exclude_id !== null) {
				$query .= " AND t.id != :exclude_id";
				$params[':exclude_id'] = $exclude_id;
			}

			$query .= " ORDER BY t.descripcion ASC";

			$statement = $conexion->prepare($query);
			foreach ($params as $key => $value) {
				$statement->bindValue($key, $value, PDO::PARAM_INT);
			}
			$statement->execute();

			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);
			$tareas = [];

			foreach ($resultados as $resultado) {
				$tareas[] = self::construct($resultado);
			}

			return $tareas;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener tareas potenciales padre: " . $e->getMessage());
		}
	}

	/**
	 * Busca tareas para autocomplete basado en descripción y nombre de proyecto.
	 *
	 * @param string   $search_query Término de búsqueda.
	 * @param int|null $exclude_id   ID de la tarea a excluir (para evitar auto-referencia).
	 * @param PDO      $conexion     Conexión PDO.
	 * @param int      $limit        Límite de resultados (por defecto 20).
	 *
	 * @return array Array de objetos Tarea que coinciden con la búsqueda.
	 * @throws Exception Si hay error en DB.
	 */
	public static function searchTasksForAutocomplete(string $search_query, ?int $exclude_id, PDO $conexion, int $limit = 20): array
	{
		try {
			// Validar que el término de búsqueda tenga al menos 2 caracteres
			if (strlen(trim($search_query)) < 2) {
				return [];
			}

			// Build the query dynamically using unique parameter names
			$query = <<<SQL
			SELECT
				t.*,
				p.descripcion AS nombre_proyecto,
				pm.descripcion AS nombre_proyecto_modulo,
				te.descripcion AS nombre_tarea_estado,
				te.bg_color,
				tp.descripcion AS nombre_tarea_padre,
				s.descripcion AS nombre_sprint
			FROM tareas t
			LEFT JOIN proyectos p ON t.id_proyecto = p.id
			LEFT JOIN proyectos_modulos pm ON t.id_proyecto_modulo = pm.id
			LEFT JOIN tareas_estados te ON t.id_tarea_estado = te.id
			LEFT JOIN tareas tp ON t.id_tarea_padre = tp.id
			LEFT JOIN sprints s ON t.id_sprint = s.id
			WHERE t.id_tarea_estado NOT IN (:estado_eliminado)
				AND (t.id_sprint IS NULL OR s.terminado = 0 OR s.terminado IS NULL)
				AND (
					t.descripcion LIKE :search_term_tarea
					OR p.descripcion LIKE :search_term_proyecto
				)
			SQL;

			// Add exclude condition if needed
			if ($exclude_id !== null) {
				$query .= " AND t.id != :exclude_id";
			}

			$query .= " ORDER BY t.descripcion ASC LIMIT " . (int)$limit;

			$statement = $conexion->prepare($query);

			// Bind parameters individually with correct types and unique names
			$search_value = '%' . trim($search_query) . '%';
			$statement->bindValue(':estado_eliminado', self::ESTADO_ELIMINADO, PDO::PARAM_INT);
			$statement->bindValue(':search_term_tarea', $search_value, PDO::PARAM_STR);
			$statement->bindValue(':search_term_proyecto', $search_value, PDO::PARAM_STR);

			if ($exclude_id !== null) {
				$statement->bindValue(':exclude_id', $exclude_id, PDO::PARAM_INT);
			}

			$statement->execute();
			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$tareas = [];
			foreach ($resultados as $resultado) {
				$tareas[] = self::construct($resultado);
			}

			return $tareas;

		} catch (PDOException $e) {
			// Add debugging information
			$debug_info = "Query: " . $query . " | Search: " . $search_query . " | Exclude ID: " . ($exclude_id ?? 'null');
			throw new Exception("Error al buscar tareas para autocomplete: " . $e->getMessage() . " | Debug: " . $debug_info);
		}
	}

	/**
	 * Verifica si una tarea puede ser padre de otra (evita referencias circulares).
	 *
	 * @param int $potential_parent_id ID de la potencial tarea padre.
	 * @param int $child_id            ID de la tarea hija.
	 * @param PDO $conexion            Conexión PDO.
	 *
	 * @return bool True si es válido, False si crearía una referencia circular.
	 * @throws Exception Si hay error en DB.
	 */
	public static function isValidParentChild(int $potential_parent_id, int $child_id, PDO $conexion): bool
	{
		// Una tarea no puede ser padre de sí misma
		if ($potential_parent_id === $child_id) {
			return false;
		}

		// Verificar si el potencial padre es descendiente del hijo (referencia circular)
		return !self::isDescendantOf($potential_parent_id, $child_id, $conexion);
	}

	/**
	 * Verifica si una tarea es descendiente de otra.
	 *
	 * @param int $task_id      ID de la tarea a verificar.
	 * @param int $ancestor_id  ID del potencial ancestro.
	 * @param PDO $conexion     Conexión PDO.
	 *
	 * @return bool True si task_id es descendiente de ancestor_id.
	 * @throws Exception Si hay error en DB.
	 */
	private static function isDescendantOf(int $task_id, int $ancestor_id, PDO $conexion): bool
	{
		try {
			$query = "SELECT id_tarea_padre FROM tareas WHERE id = :id";
			$statement = $conexion->prepare($query);
			$statement->bindValue(':id', $task_id, PDO::PARAM_INT);
			$statement->execute();

			$result = $statement->fetch(PDO::FETCH_ASSOC);
			if (!$result || $result['id_tarea_padre'] === null) {
				return false; // No tiene padre, no es descendiente
			}

			$parent_id = (int)$result['id_tarea_padre'];
			if ($parent_id === $ancestor_id) {
				return true; // Es hijo directo
			}

			// Verificar recursivamente
			return self::isDescendantOf($parent_id, $ancestor_id, $conexion);

		} catch (PDOException $e) {
			throw new Exception("Error al verificar descendencia: " . $e->getMessage());
		}
	}

	// --- Sprint Relationship Methods ---

	/**
	 * Obtiene las tareas asociadas a un sprint específico.
	 *
	 * @param int $id_sprint ID del sprint.
	 * @param PDO $conexion  Conexión PDO.
	 *
	 * @return array Array de objetos Tarea asociadas al sprint.
	 * @throws Exception Si hay error en DB.
	 */
	public static function getTasksBySprint(int $id_sprint, PDO $conexion): array
	{
		return self::get_list(['id_sprint' => $id_sprint], $conexion);
	}

	/**
	 * Obtiene el objeto Sprint asociado a esta tarea.
	 *
	 * @param PDO $conexion Conexión PDO.
	 *
	 * @return \App\classes\Sprint|null Objeto Sprint o null si no está asociado a ningún sprint.
	 * @throws Exception Si hay error en DB.
	 */
	public function getSprint(PDO $conexion): ?\App\classes\Sprint
	{
		if ($this->id_sprint === null) {
			return null;
		}

		return \App\classes\Sprint::get_by_id($this->id_sprint, $conexion);
	}

	/**
	 * Actualiza la asociación de Sprint para una tarea y todas sus tareas hijas.
	 * Esto asegura que las relaciones padre-hijo se mantengan durante las operaciones de Sprint.
	 *
	 * @param int      $tarea_id  ID de la tarea padre.
	 * @param int|null $sprint_id ID del Sprint a asociar (null para desasociar).
	 * @param PDO      $conexion  Conexión PDO.
	 *
	 * @return bool True si la operación fue exitosa, False en caso contrario.
	 * @throws Exception Si hay error en DB.
	 */
	public static function updateSprintAssociationCascading(int $tarea_id, ?int $sprint_id, PDO $conexion): bool
	{
		try {
			// Iniciar transacción para asegurar consistencia
			$conexion->beginTransaction();

			// 1. Obtener la tarea padre
			$tarea_padre = self::get($tarea_id, $conexion);
			if (!$tarea_padre) {
				throw new Exception("Tarea no encontrada (ID: $tarea_id)");
			}

			// 2. Actualizar la tarea padre
			$tarea_padre->setIdSprint($sprint_id);
			if (!$tarea_padre->modificar($conexion)) {
				throw new Exception("Error al actualizar la tarea padre (ID: $tarea_id)");
			}

			// 3. Obtener todas las tareas hijas
			$tareas_hijas = self::getChildTasks($tarea_id, $conexion);

			// 4. Actualizar todas las tareas hijas con la misma asociación de Sprint
			foreach ($tareas_hijas as $tarea_hija) {
				$tarea_hija->setIdSprint($sprint_id);
				if (!$tarea_hija->modificar($conexion)) {
					throw new Exception("Error al actualizar la tarea hija (ID: {$tarea_hija->getId()})");
				}
			}

			// Confirmar transacción
			$conexion->commit();
			return true;

		} catch (Exception $e) {
			// Revertir transacción en caso de error
			$conexion->rollBack();
			throw new Exception("Error al actualizar asociación de Sprint en cascada: " . $e->getMessage());
		}
	}

	/**
	 * Actualiza la asociación de Sprint para múltiples tareas de forma masiva.
	 * Útil para operaciones que afectan múltiples tareas simultáneamente.
	 *
	 * @param array    $tarea_ids Array de IDs de tareas a actualizar.
	 * @param int|null $sprint_id ID del Sprint a asociar (null para desasociar).
	 * @param PDO      $conexion  Conexión PDO.
	 *
	 * @return bool True si la operación fue exitosa, False en caso contrario.
	 * @throws Exception Si hay error en DB.
	 */
	public static function updateSprintAssociationBulk(array $tarea_ids, ?int $sprint_id, PDO $conexion): bool
	{
		if (empty($tarea_ids)) {
			return true; // No hay tareas que actualizar
		}

		try {
			// Iniciar transacción
			$conexion->beginTransaction();

			// Preparar consulta de actualización masiva
			$placeholders = str_repeat('?,', count($tarea_ids) - 1) . '?';
			$query = "UPDATE tareas SET id_sprint = ? WHERE id IN ($placeholders)";

			$statement = $conexion->prepare($query);

			// Bind parameters: sprint_id first, then all task IDs
			$params = [$sprint_id];
			$params = array_merge($params, $tarea_ids);



			$success = $statement->execute($params);

			if (!$success) {
				throw new Exception("Error al ejecutar actualización masiva de Sprint");
			}

			// Confirmar transacción
			$conexion->commit();
			return true;

		} catch (Exception $e) {
			// Revertir transacción en caso de error
			$conexion->rollBack();
			throw new Exception("Error al actualizar asociación de Sprint masiva: " . $e->getMessage());
		}
	}

	/**
	 * Obtiene tareas históricas (terminadas de sprints terminados) con filtros opcionales.
	 *
	 * @param array $parametros Array con filtros opcionales.
	 * @param PDO   $conexion   Conexión PDO.
	 *
	 * @return array Array de objetos Tarea históricas.
	 * @throws Exception Si hay error en DB.
	 */
	public static function getHistoricalTasks(array $parametros, PDO $conexion): array
	{
		try {
			$id_proyecto = $parametros['id_proyecto'] ?? null;
			$id_sprint = $parametros['id_sprint'] ?? null;
			$fecha_inicio = $parametros['fecha_inicio'] ?? null;
			$fecha_fin = $parametros['fecha_fin'] ?? null;
			$busqueda = $parametros['busqueda'] ?? null;

			if (!$id_proyecto) {
				throw new Exception("ID de proyecto es requerido para consultas históricas.");
			}

			// Base query for historical tasks
			$query = <<<SQL
			SELECT
				t.*,
				p.descripcion AS nombre_proyecto,
				pm.descripcion AS nombre_proyecto_modulo,
				te.descripcion AS nombre_tarea_estado,
				te.bg_color,
				tp.descripcion AS nombre_tarea_padre,
				s.descripcion AS nombre_sprint
			FROM tareas t
			LEFT JOIN proyectos p ON t.id_proyecto = p.id
			LEFT JOIN proyectos_modulos pm ON t.id_proyecto_modulo = pm.id
			LEFT JOIN tareas_estados te ON t.id_tarea_estado = te.id
			LEFT JOIN tareas tp ON t.id_tarea_padre = tp.id
			INNER JOIN sprints s ON t.id_sprint = s.id
			WHERE t.id_proyecto = :id_proyecto
				AND t.id_sprint IS NOT NULL
				AND s.terminado = 1
				AND t.id_tarea_estado = :estado_terminado
			SQL;

			$params = [
				':id_proyecto' => $id_proyecto,
				':estado_terminado' => self::ESTADO_TERMINADO
			];

			// Add optional filters
			if ($id_sprint) {
				$query .= " AND t.id_sprint = :id_sprint";
				$params[':id_sprint'] = $id_sprint;
			}

			if ($fecha_inicio) {
				$query .= " AND DATE(t.fecha_terminacion) >= :fecha_inicio";
				$params[':fecha_inicio'] = $fecha_inicio;
			}

			if ($fecha_fin) {
				$query .= " AND DATE(t.fecha_terminacion) <= :fecha_fin";
				$params[':fecha_fin'] = $fecha_fin;
			}

			if ($busqueda) {
				$query .= " AND t.descripcion LIKE :busqueda";
				$params[':busqueda'] = '%' . $busqueda . '%';
			}

			$query .= " ORDER BY t.fecha_terminacion DESC, t.id DESC";

			$statement = $conexion->prepare($query);
			foreach ($params as $param => $value) {
				$statement->bindValue($param, $value);
			}
			$statement->execute();

			$resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

			$listado = [];
			foreach ($resultados as $resultado) {
				$listado[] = self::construct($resultado);
			}
			return $listado;

		} catch (PDOException $e) {
			throw new Exception("Error al obtener tareas históricas: " . $e->getMessage());
		}
	}
}
