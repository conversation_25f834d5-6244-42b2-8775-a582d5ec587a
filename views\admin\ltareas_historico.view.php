<?php
#region region DOCS

/** @var Tarea[] $tareas_historicas */
/** @var Sprint[] $sprints_terminados */
/** @var int|null $filtro_proyecto_id */
/** @var int|null $filtro_sprint_id */
/** @var string|null $filtro_fecha_inicio */
/** @var string|null $filtro_fecha_fin */
/** @var string|null $filtro_busqueda */
/** @var string|null $nombre_proyecto_filtro */
/** @var string $success_text */
/** @var string $success_display */
/** @var string $error_text */
/** @var string $error_display */

use App\classes\Tarea;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="es" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Tareas <PERSON></title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="Consulta de Tareas Históricas" name="description"/>
	<meta content="" name="author"/>

	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/admin/general/head.view.php'; ?>
	<link href="<?php echo RUTA_ADM_ASSETS ?>plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker.css" rel="stylesheet"/>
	<?php #endregion HEAD ?>
</head>
<body>
	<!-- BEGIN #app -->
	<div id="app" class="app-header-fixed app-sidebar-fixed">
		<?php #region region HEADER ?>
		<?php require_once __ROOT__ . '/views/admin/general/header.view.php'; ?>
		<?php #endregion HEADER ?>

		<?php #region region SIDEBAR ?>
		<?php require_once __ROOT__ . '/views/admin/general/sidebar.view.php'; ?>
		<?php #endregion SIDEBAR ?>

		<!-- BEGIN #content -->
		<div id="content" class="app-content">
			<?php #region region FLASH MESSAGES ?>
			<?php if ($success_display === 'show'): ?>
				<div class="alert alert-success alert-dismissible fade show" role="alert">
					<?php echo htmlspecialchars($success_text); ?>
					<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
				</div>
			<?php endif; ?>

			<?php if ($error_display === 'show'): ?>
				<div class="alert alert-danger alert-dismissible fade show" role="alert">
					<?php echo htmlspecialchars($error_text); ?>
					<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
				</div>
			<?php endif; ?>
			<?php #endregion FLASH MESSAGES ?>

			<?php #region region PAGE HEADER ?>
			<div class="d-flex align-items-center mb-3">
				<div>
					<h4 class="mb-0">Tareas Históricas<?php if ($nombre_proyecto_filtro): ?> - <?php echo htmlspecialchars($nombre_proyecto_filtro); ?><?php endif; ?></h4>
					<p class="mb-0 text-muted">Consulta de tareas terminadas del sistema</p>
				</div>
				<div class="ms-auto">
					<a href="lproyectos" class="btn btn-outline-primary"><i class="fa fa-folder fa-fw me-1"></i> Ver Proyectos</a>
				</div>
			</div>

			<hr>
			<?php #endregion PAGE HEADER ?>

			<?php if (!$filtro_proyecto_id): ?>
				<div class="alert alert-info">
					<i class="fa fa-info-circle me-2"></i>
					Para consultar tareas históricas, debe acceder desde la página de proyectos usando el botón "Consultar tareas terminadas".
				</div>
			<?php else: ?>

			<?php #region region FILTERS ?>
			<div class="panel panel-inverse no-border-radious">
				<div class="panel-heading no-border-radious">
					<h4 class="panel-title">Filtros de Búsqueda</h4>
				</div>
				<div class="panel-body">
					<form method="POST" action="listado-tareas-historico" class="row g-3">
						<div class="col-md-3">
							<label for="sprint" class="form-label">Sprint</label>
							<select class="form-select" id="sprint" name="sprint">
								<option value="">Todos los sprints</option>
								<?php foreach ($sprints_terminados as $sprint): ?>
									<option value="<?php echo $sprint->getId(); ?>" 
									        <?php echo $filtro_sprint_id == $sprint->getId() ? 'selected' : ''; ?>>
										<?php echo htmlspecialchars($sprint->getDescripcion()); ?>
									</option>
								<?php endforeach; ?>
							</select>
						</div>
						<div class="col-md-2">
							<label for="fecha_inicio" class="form-label">Fecha Inicio</label>
							<div class="input-group">
								<input type="text" class="form-control datepicker" id="fecha_inicio" name="fecha_inicio"
								       value="<?php echo htmlspecialchars($filtro_fecha_inicio ?? ''); ?>"
								       autocomplete="off" placeholder="yyyy-mm-dd">
								<span class="input-group-text"><i class="fa fa-calendar-alt"></i></span>
							</div>
						</div>
						<div class="col-md-2">
							<label for="fecha_fin" class="form-label">Fecha Fin</label>
							<div class="input-group">
								<input type="text" class="form-control datepicker" id="fecha_fin" name="fecha_fin"
								       value="<?php echo htmlspecialchars($filtro_fecha_fin ?? ''); ?>"
								       autocomplete="off" placeholder="yyyy-mm-dd">
								<span class="input-group-text"><i class="fa fa-calendar-alt"></i></span>
							</div>
						</div>
						<div class="col-md-3">
							<label for="busqueda" class="form-label">Buscar en descripción</label>
							<input type="text" class="form-control" id="busqueda" name="busqueda" 
							       placeholder="Texto a buscar..." 
							       value="<?php echo htmlspecialchars($filtro_busqueda ?? ''); ?>">
						</div>
						<div class="col-md-2 d-flex align-items-end">
							<button type="submit" class="btn btn-primary me-2">
								<i class="fa fa-search"></i> Buscar
							</button>
							<a href="listado-tareas-historico" class="btn btn-secondary">
								<i class="fa fa-times"></i> Limpiar
							</a>
						</div>
					</form>
				</div>
			</div>
			<?php #endregion FILTERS ?>

			<?php #region region PANEL TAREAS HISTORICAS ?>
			<div class="panel panel-inverse mt-3 no-border-radious">
				<div class="panel-heading no-border-radious">
					<h4 class="panel-title">
						Tareas Históricas
						<?php if ($nombre_proyecto_filtro): ?>
							- <?php echo htmlspecialchars($nombre_proyecto_filtro); ?>
						<?php endif; ?>
						<?php if (count($tareas_historicas) > 0): ?>
							(<?php echo count($tareas_historicas); ?> registros)
						<?php endif; ?>
					</h4>
				</div>
				<!-- BEGIN PANEL body -->
				<div class="table-nowrap" style="overflow: auto">
					<?php #region region TABLE TAREAS HISTORICAS ?>
					<table class="table table-hover table-sm">
						<thead>
						<tr>
							<th class="text-center" style="width: 80px;">Acciones</th>
							<th class="text-center" style="width: 50px;">#</th>
							<th>Sprint</th>
							<th>Módulo</th>
							<th>Descripción</th>
							<th class="text-center">Fecha Terminación</th>
						</tr>
						</thead>
						<tbody class="fs-12px" id="tarea-historica-table-body">
						<?php if (!empty($tareas_historicas)): ?>
							<?php foreach ($tareas_historicas as $tarea): ?>
								<tr data-tarea-id="<?php echo $tarea->getId(); ?>">
									<td class="text-center align-middle">
										<button type="button" class="btn btn-xs btn-outline-info btn-ver-agentes-historico"
										        title="Ver Agentes de la Tarea"
										        data-bs-toggle="modal"
										        data-bs-target="#verAgentesHistoricoModal"
										        data-tarea-id="<?php echo $tarea->getId(); ?>"
										        data-tarea-descripcion="<?php echo htmlspecialchars($tarea->getDescripcion() ?? ''); ?>">
											<i class="fa fa-users"></i>
										</button>
									</td>
									<td class="text-center align-middle"><?php echo htmlspecialchars((string)$tarea->getId()); ?></td>
									<td class="align-middle"><?php echo htmlspecialchars($tarea->getNombreSprint() ?? 'N/A'); ?></td>
									<td class="align-middle"><?php echo htmlspecialchars($tarea->getNombreProyectoModulo() ?? 'N/A'); ?></td>
									<td class="align-middle"><?php echo htmlspecialchars($tarea->getDescripcion() ?? 'N/A'); ?></td>
									<td class="text-center align-middle">
										<?php
										$fecha_terminacion = $tarea->getFechaTerminacion();
										echo $fecha_terminacion ? date('Y-m-d', strtotime($fecha_terminacion)) : 'N/A';
										?>
									</td>
								</tr>
							<?php endforeach; ?>
						<?php else: ?>
							<tr>
								<td colspan="6" class="text-center align-middle">
									<?php if ($filtro_sprint_id || $filtro_fecha_inicio || $filtro_fecha_fin || $filtro_busqueda): ?>
										No se encontraron tareas históricas con los filtros aplicados.
									<?php else: ?>
										Aplique filtros para consultar las tareas históricas.
									<?php endif; ?>
								</td>
							</tr>
						<?php endif; ?>
						</tbody>
					</table>
					<?php #endregion TABLE TAREAS HISTORICAS ?>
				</div>
				<!-- END PANEL body -->
			</div>
			<?php #endregion PANEL TAREAS HISTORICAS ?>

			<?php endif; ?>

		</div>
		<!-- END #content -->

		<!-- Ver Agentes Histórico Modal (Read-only) -->
		<div class="modal fade" id="verAgentesHistoricoModal" tabindex="-1" aria-labelledby="verAgentesHistoricoModalLabel" aria-hidden="true">
			<div class="modal-dialog modal-lg">
				<div class="modal-content">
					<div class="modal-header">
						<h5 class="modal-title" id="verAgentesHistoricoModalLabel">Agentes Asignados - Histórico</h5>
						<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
					</div>
					<div class="modal-body">
						<div class="mb-3">
							<strong>Tarea:</strong> <span id="modal-tarea-descripcion-historico"></span>
						</div>

						<!-- Loading State -->
						<div id="agentes-loading-historico" style="display: none;">
							<div class="text-center">
								<div class="spinner-border" role="status">
									<span class="visually-hidden">Cargando...</span>
								</div>
								<p class="mt-2">Cargando agentes...</p>
							</div>
						</div>

						<!-- Error State -->
						<div id="agentes-error-historico" class="alert alert-danger" style="display: none;"></div>

						<!-- Content -->
						<div id="agentes-content-historico" style="display: none;">
							<div class="table-responsive">
								<table class="table table-sm table-dark">
									<thead class="table-dark">
									<tr>
										<th>Agente</th>
										<th>Módulo</th>
										<th class="text-center">Costo (USD)</th>
										<th class="text-center">Mensajes</th>
									</tr>
									</thead>
									<tbody id="agentes-table-body-historico">
									<!-- Content will be populated via AJAX -->
									</tbody>
								</table>
							</div>
						</div>
					</div>
					<div class="modal-footer">
						<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cerrar</button>
					</div>
				</div>
			</div>
		</div>

		<!-- BEGIN scroll-top-btn -->
		<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
		<!-- END scroll-top-btn -->
	</div>
	<!-- END #app -->

	<?php #region region JS ?>
	<?php require_once __ROOT__ . '/views/admin/general/core_js.view.php'; ?>
	<script src="<?php echo RUTA_ADM_ASSETS ?>plugins/bootstrap-datepicker/dist/js/bootstrap-datepicker.js"></script>
	<script src="<?php echo RUTA_RESOURCES ?>js/datepickerini.js"></script>

	<!-- ================== BEGIN PAGE LEVEL JS ================== -->
	<script>
	document.addEventListener('DOMContentLoaded', function () {
		// Handle Ver Agentes Histórico button clicks
		document.addEventListener('click', function(event) {
			const verAgentesButton = event.target.closest('.btn-ver-agentes-historico');

			if (verAgentesButton) {
				event.preventDefault();

				const tareaId = verAgentesButton.dataset.tareaId;
				const tareaDescripcion = verAgentesButton.dataset.tareaDescripcion;

				// Set the task description in the modal
				document.getElementById('modal-tarea-descripcion-historico').textContent = tareaDescripcion;

				// Show loading state
				document.getElementById('agentes-loading-historico').style.display = 'block';
				document.getElementById('agentes-content-historico').style.display = 'none';
				document.getElementById('agentes-error-historico').style.display = 'none';

				// Load TareaAgente data via AJAX
				loadTareaAgentesHistorico(tareaId);
			}
		});

		// Function to load TareaAgente data for historical view
		function loadTareaAgentesHistorico(tareaId) {
			const formData = new FormData();
			formData.append('action', 'get_tarea_agentes');
			formData.append('tarea_id', tareaId);
			formData.append('is_ajax', '1');

			fetch('listado-tareas-historico', {
				method: 'POST',
				body: formData,
				headers: {
					'X-Requested-With': 'XMLHttpRequest'
				}
			})
			.then(response => {
				if (!response.ok) {
					throw new Error(`HTTP error! Status: ${response.status}`);
				}
				return response.json();
			})
			.then(data => {
				// Hide loading state
				document.getElementById('agentes-loading-historico').style.display = 'none';

				if (data.success) {
					// Populate the table
					const tableBody = document.getElementById('agentes-table-body-historico');
					tableBody.innerHTML = '';

					if (data.tarea_agentes && data.tarea_agentes.length > 0) {
						data.tarea_agentes.forEach(agente => {
							const row = document.createElement('tr');
							row.innerHTML = `
								<td>${agente.agente_descripcion || 'N/A'}</td>
								<td>${agente.modulo_nombre || 'N/A'}</td>
								<td class="text-center">$${parseFloat(agente.costo_usd || 0).toFixed(2)}</td>
								<td class="text-center">${agente.n_mensajes || 0}</td>
							`;
							tableBody.appendChild(row);
						});
					} else {
						const row = document.createElement('tr');
						row.innerHTML = '<td colspan="4" class="text-center">No hay agentes asignados a esta tarea.</td>';
						tableBody.appendChild(row);
					}

					// Show content
					document.getElementById('agentes-content-historico').style.display = 'block';
				} else {
					// Show error
					document.getElementById('agentes-error-historico').textContent = data.message || 'Error al cargar los agentes';
					document.getElementById('agentes-error-historico').style.display = 'block';
				}
			})
			.catch(error => {
				console.error('Error loading agentes:', error);
				// Hide loading state
				document.getElementById('agentes-loading-historico').style.display = 'none';
				// Show error
				document.getElementById('agentes-error-historico').textContent = 'Error de comunicación: ' + error.message;
				document.getElementById('agentes-error-historico').style.display = 'block';
			});
		}
	});
	</script>
	<!-- ================== END PAGE LEVEL JS ================== -->

	<?php #endregion JS ?>

</body>
</html>
