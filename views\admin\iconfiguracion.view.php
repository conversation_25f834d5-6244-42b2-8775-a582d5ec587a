<?php
#region region DOCS

/** @var Configuracion $configuracion */
/** @var string $error_text */
/** @var string $error_display */

use App\classes\Configuracion;

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Nueva Configuración</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="" name="description"/>
	<meta content="" name="author"/>
	
	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/admin/general/head.view.php'; ?>
	<?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/admin/general/header.view.php'; ?>
	
	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/admin/general/sidebar.view.php'; ?>
	
	<!-- BEGIN #content -->
	<div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
		<div class="d-flex align-items-center mb-3">
			<div>
				<h4 class="mb-0">Nueva Configuración</h4>
				<p class="mb-0 text-muted">Crear una nueva configuración del sistema</p>
			</div>
			<div class="ms-auto">
				<a href="lconfiguraciones" class="btn btn-secondary"><i class="fa fa-arrow-left fa-fw me-1"></i> Volver al listado</a>
			</div>
		</div>
		
		<hr>
		<?php #endregion PAGE HEADER ?>
		
		<?php #region region FORM PANEL ?>
		<div class="panel panel-inverse mt-3 no-border-radious">
			<div class="panel-heading no-border-radious">
				<h4 class="panel-title">Formulario de Configuración</h4>
			</div>
			<div class="panel-body">
				<?php if ($error_display !== 'none'): ?>
					<div class="alert alert-danger" style="display: <?php echo $error_display; ?>">
						<?php echo $error_text; ?>
					</div>
				<?php endif; ?>
				
				<form method="POST" action="iconfiguracion">
					<div class="mb-3">
						<label for="descripcion" class="form-label">Descripción <span class="text-danger">*</span></label>
						<input type="text" class="form-control" id="descripcion" name="descripcion" 
						       value="<?php echo htmlspecialchars($configuracion->getDescripcion() ?? ''); ?>" required>
						<div class="form-text">Nombre descriptivo de la configuración</div>
					</div>
					
					<div class="mb-3">
						<label for="valor" class="form-label">Valor</label>
						<input type="text" class="form-control" id="valor" name="valor"
						       value="<?php echo htmlspecialchars($configuracion->getValor() ?? ''); ?>">
						<div class="form-text">Valor de la configuración</div>
					</div>

					<div class="mb-3">
						<div class="form-check">
							<input type="checkbox" class="form-check-input" id="es_editable" name="es_editable" value="1"
							       <?php echo ($configuracion->getEsEditable() == 1) ? 'checked' : ''; ?>>
							<label class="form-check-label" for="es_editable">
								Es editable
							</label>
							<div class="form-text">Permite que esta configuración sea editada posteriormente</div>
						</div>
					</div>

					<div class="mt-4">
						<button type="submit" class="btn btn-primary">Guardar Configuración</button>
						<a href="lconfiguraciones" class="btn btn-default ms-2">Cancelar</a>
					</div>
				</form>
			</div>
		</div>
		<?php #endregion FORM PANEL ?>
	</div>
	<!-- END #content -->
	
	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<?php require_once __ROOT__ . '/views/admin/general/core_js.view.php'; ?>
<?php #endregion JS ?>

</body>
</html>
