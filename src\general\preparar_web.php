<?php
// global $conexion, $lastmodule; // Declaration moved down

// BEGIN redirect SSL
// funcion para obligar todo el trafico de la pagina a traves del canal SSL.
if ((empty($_SERVER['HTTPS']) || $_SERVER['HTTPS'] === "off") && ($_SERVER['HTTP_HOST'] !== "localhost" && $_SERVER['HTTP_HOST'] !== "127.0.0.1")) {
	$location = 'https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
	header('HTTP/1.1 301 Moved Permanently');
	header('Location: ' . $location);
	exit();
}
// END redirect SSL

use \App\classes\Configuracion;

require_once __ROOT__ . '/src/query/connection.php';
require_once __ROOT__ . '/vendor/autoload.php';

global $conexion, $lastmodule;


// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en form_aliados_comerciales.php (POST).");
	$response['message'] = 'Error crítico: No se pudo conectar a la base de datos.';
	http_response_code(503); // Service Unavailable
	echo json_encode($response);
	exit;
}

try {
	$error_text      = '';
	$error_display   = 'hide';
	$success_text 	 = '';
	$success_display = 'hide';
	
	$configuraciones   = Configuracion::get_list([], $conexion);
	$celular_ventas    = '';
	$logo_texto_azul   = '';
	$logo_texto_blanco = '';

	//obtener configuraciones del panel administrativo
	foreach ($configuraciones as $configuracion) {
		if($configuracion->getDescripcion() == 'Celular ventas'){
			$celular_ventas = $configuracion->getValor();
		}
		if($configuracion->getId() == 5){
			$logo_texto_azul = $configuracion->getValor();
		}
		if($configuracion->getId() == 6){
			$logo_texto_blanco = $configuracion->getValor();
		}
	}
	
} catch (Exception $e) {
	$error_display = 'show';
	$error_text = $e->getMessage();
}


?>
