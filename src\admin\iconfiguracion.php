<?php

// Iniciar sesión si es necesario
use App\classes\Configuracion;

if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

/** @var PDO $conexion */
global $conexion;

require_once dirname(__FILE__, 3) . '/config/config.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/general/preparar.php';

// Check for valid database connection before proceeding
if (!$conexion instanceof PDO) {
	error_log("Error crítico: No hay conexión a la base de datos en iconfiguracion.php");
	$_SESSION['flash_message_error'] = 'Error crítico: No se pudo conectar a la base de datos.';
	header('Location: lconfiguraciones');
	exit;
}

#region region init variables
$configuracion = new Configuracion();
$error_text = '';
$error_display = 'none';
#endregion init variables

#region region Handle POST Request
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
	// Get and sanitize form data
	$descripcion = filter_input(INPUT_POST, 'descripcion', FILTER_SANITIZE_SPECIAL_CHARS);
	$valor = filter_input(INPUT_POST, 'valor', FILTER_SANITIZE_SPECIAL_CHARS);
	$es_editable = isset($_POST['es_editable']) ? 1 : 0; // Checkbox handling
	$descripcion = trim($descripcion ?? '');
	$valor = trim($valor ?? '');
	
	// Validate required fields
	if (empty($descripcion)) {
		$error_display = 'block';
		$error_text = 'La descripción es requerida.';
	} else {
		try {
			// Create new configuration
			$configuracion = new Configuracion();
			$configuracion->setDescripcion($descripcion);
			$configuracion->setValor($valor);
			$configuracion->setEsEditable($es_editable);
			
			// Save to database
			$success = $configuracion->guardar($conexion);
			
			if ($success) {
				// Set success message and redirect to list page
				$_SESSION['flash_message_success'] = 'Configuración creada correctamente.';
				header('Location: lconfiguraciones');
				exit;
			} else {
				$error_display = 'block';
				$error_text = 'Error al guardar la configuración en la base de datos.';
			}
		} catch (Exception $e) {
			$error_display = 'block';
			$error_text = 'Error: ' . $e->getMessage();
		}
	}
}
#endregion Handle POST Request

require_once __ROOT__ . '/views/admin/iconfiguracion.view.php';

?>
